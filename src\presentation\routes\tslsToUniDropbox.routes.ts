// TslsToUniDropbox routes
import { FastifyInstance } from 'fastify';
import { TslsToUniDropboxController } from '../controllers/TslsToUniDropbox.Controller.js';
import {
  TslsToUniDropboxCreateDto,
  TslsToUniDropboxUpdateDto
} from '../../application/dto/dropbox.dto.js';
import { verifyJWT } from '../middleware/auth.js';

export default async function tslsToUniDropboxRoutes(fastify: FastifyInstance) {
  const dropboxController = new TslsToUniDropboxController(fastify.prisma);

  // Swagger schemas for documentation
  const dropboxResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: {
        type: 'object',
        properties: {
          tslsToUniDropboxId: { type: 'string' },
          tslsToUniDropboxName: { type: 'string' },
          universityId: { type: 'string' },
          year: { type: 'number' },
          modeOfStudy: { type: 'string' },
          term: { type: 'number' },
          batchNumber: { type: 'number' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          openingDate: { type: 'string', format: 'date-time' },
          closingDate: { type: 'string', format: 'date-time' },
          isOpenStatus: { type: 'boolean' }
        }
      }
    }
  };

  const errorResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      errors: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  // Create TSLS to Uni dropbox
  fastify.post<{ Body: TslsToUniDropboxCreateDto }>('/create-tsls-to-uni-dropbox', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Create TSLS to University dropbox',
      tags: ['TSLS to University Dropbox'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['universityId', 'year', 'modeOfStudy', 'term', 'openingDate', 'closingDate', 'isOpenStatus'],
        properties: {
          universityId: { type: 'string', format: 'uuid' },
          year: { type: 'integer', minimum: 2025, maximum: 2100 },
          modeOfStudy: { type: 'string', enum: ['Semester', 'Quarter', 'Trimester'] },
          term: { type: 'integer', minimum: 1, maximum: 4 },
          resetBatch: { type: 'boolean', default: false },
          openingDate: { type: 'string', format: 'date-time' },
          closingDate: { type: 'string', format: 'date-time' },
          isOpenStatus: { type: 'boolean' }
        }
      },
      response: {
        201: dropboxResponseSchema,
        400: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, dropboxController.createTslsToUniDropbox.bind(dropboxController));

  // Update TSLS to Uni dropbox
  fastify.put<{
    Params: { dropboxId: string };
    Body: TslsToUniDropboxUpdateDto;
  }>('/update-tsls-to-uni-dropbox/:dropboxId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Update TSLS to University dropbox',
      tags: ['TSLS to University Dropbox'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          dropboxId: { type: 'string', format: 'uuid' }
        },
        required: ['dropboxId']
      },
      body: {
        type: 'object',
        required: ['openingDate', 'closingDate', 'isOpenStatus'],
        properties: {
          openingDate: { type: 'string', format: 'date-time' },
          closingDate: { type: 'string', format: 'date-time' },
          isOpenStatus: { type: 'boolean' }
        }
      },
      response: {
        200: dropboxResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, dropboxController.updateTslsToUniDropbox.bind(dropboxController));

  // Get dropbox by ID
  fastify.get<{ Params: { dropboxId: string } }>('/get-dropbox-by-id/:dropboxId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get TSLS to University dropbox by ID',
      tags: ['TSLS to University Dropbox'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          dropboxId: { type: 'string', format: 'uuid' }
        },
        required: ['dropboxId']
      },
      response: {
        200: dropboxResponseSchema.properties.data,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, dropboxController.getDropboxById.bind(dropboxController));

  // Get dropbox by university
  fastify.get<{ Params: { universityId: string } }>('/get-dropbox-by-university/:universityId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get TSLS to University dropboxes by university',
      tags: ['TSLS to University Dropbox'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          universityId: { type: 'string', format: 'uuid' }
        },
        required: ['universityId']
      },
      response: {
        200: {
          type: 'array',
          items: dropboxResponseSchema.properties.data
        },
        500: errorResponseSchema
      }
    }
  }, dropboxController.getDropboxByUniversity.bind(dropboxController));

  // Get universities with dropboxes
  fastify.get('/get-universities-with-dropboxes', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get universities with dropboxes',
      tags: ['TSLS to University Dropbox'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              universityId: { type: 'string' },
              universityName: { type: 'string' },
              dropboxCount: { type: 'number' }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, dropboxController.getUniversitiesWithDropboxes.bind(dropboxController));
}
